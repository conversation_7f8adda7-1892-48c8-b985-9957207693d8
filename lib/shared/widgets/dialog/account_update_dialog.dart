import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/models/apis/user.dart';
import 'package:wd/core/models/entities/system_config_entity.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/common_textfield.dart';
import 'package:wd/shared/widgets/dialog/currency_switch_dialog.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';
import 'package:wd/injection_container.dart';

import '../../../features/routers/navigator_utils.dart';

class AccountUpdateDialog {
  AccountUpdateDialog();

  show(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return BlocProvider(
          create: (context) => AccountUpdateCubit(),
          child: const _AccountUpdateDialogContent(),
        );
      },
    );
  }
}

class _AccountUpdateDialogContent extends StatelessWidget {
  const _AccountUpdateDialogContent();

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: Stack(
          children: [
            Container(
              width: 362.gw,
              padding: EdgeInsets.all(24.gw),
              decoration: BoxDecoration(
                image: const DecorationImage(image: AssetImage("assets/images/alert/bg_common.png"), fit: BoxFit.fill),
                borderRadius: BorderRadius.circular(16.gw),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(height: 8.gw), // Space for close button
                  _buildTitle(context),
                  SizedBox(height: 40.gw),
                  _buildForm(context),
                ],
              ),
            ),
            _buildCloseButton(context),
          ],
        ),
      ),
    );
  }

  Widget _buildCloseButton(BuildContext context) {
    return Positioned(
      top: 8.gw,
      right: 8.gw,
      child: GestureDetector(
        onTap: () => Navigator.of(context).pop(),
        child: Container(
          width: 32.gw,
          height: 32.gw,
          decoration: BoxDecoration(
            color: context.colorTheme.textSecondary.withOpacity(0.1),
            border: Border.all(color: context.colorTheme.textTitle),
            borderRadius: BorderRadius.circular(16.gw),
          ),
          child: Icon(
            Icons.close,
            color: context.colorTheme.textSecondary,
            size: 16.gw,
          ),
        ),
      ),
    );
  }

  Widget _buildTitle(BuildContext context) {
    return Row(mainAxisAlignment: MainAxisAlignment.center, children: [
      AneText('add'.tr(), style: context.textTheme.secondary.fs24.w600),
      SizedBox(width: 5.gw),
      AneText(
        "account".tr(),
        style: context.textTheme.primary.fs24.w600.copyWith(color: context.colorTheme.btnBgPrimary),
      ),
      SizedBox(width: 5.gw),
      AneText("information".tr(), style: context.textTheme.secondary.fs24.w600),
    ]);
  }

  Widget _buildForm(BuildContext context) {
    return BlocBuilder<AccountUpdateCubit, AccountUpdateState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCurrencyField(context, state),
            SizedBox(height: 24.gw),
            _buildNicknameField(context, state),
            SizedBox(height: 40.gw),
            _buildSubmitButton(context, state),
          ],
        );
      },
    );
  }

  Widget _buildCurrencyField(BuildContext context, AccountUpdateState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            AneText(
              '*',
              style: context.textTheme.secondary.fs16.copyWith(
                color: const Color(0xFFDE2200),
              ),
            ),
            SizedBox(width: 5.gw),
            AneText(
              'select_currency'.tr(),
              style: context.textTheme.title.fs16,
            ),
          ],
        ),
        SizedBox(height: 16.gw),
        GestureDetector(
          onTap: () => context.read<AccountUpdateCubit>().showCurrencyPicker(context),
          child: Container(
            width: 314.gw,
            height: 48.gw,
            padding: EdgeInsets.symmetric(horizontal: 16.gw),
            decoration: BoxDecoration(
              border: Border.all(color: context.colorTheme.borderA),
              borderRadius: BorderRadius.circular(12.gw),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                AneText(
                  state.selectedCurrency?.name ?? 'select_currency'.tr(),
                  style: context.textTheme.regular.copyWith(
                    fontSize: 14.gw,
                    color: state.selectedCurrency != null
                        ? context.colorTheme.textTitle
                        : context.colorTheme.textHighlight,
                  ),
                ),
                Icon(
                  Icons.arrow_drop_down,
                  color: context.colorTheme.textHighlight,
                  size: 22.gw,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildNicknameField(BuildContext context, AccountUpdateState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AneText(
          'set_nickname'.tr(),
          style: context.textTheme.title.fs16,
        ),
        SizedBox(height: 16.gw),
        CommonTextField(
          fillColor: context.theme.cardColor,
          isShowBorder: true,
          enabledBorderColor: context.colorTheme.borderA,
          controller: state.nicknameController,
          hintText: 'enter_nickname_optional'.tr(),
          onChanged: (value) => context.read<AccountUpdateCubit>().updateNickname(value),
        ),
      ],
    );
  }

  Widget _buildSubmitButton(BuildContext context, AccountUpdateState state) {
    return SizedBox(
      width: 314.gw,
      child: CommonButton(
        title: 'submit'.tr(),
        style: CommonButtonStyle.primary,
        onPressed: state.isSubmitting ? null : () => context.read<AccountUpdateCubit>().submit(context),
      ),
    );
  }
}

// Cubit for managing dialog state
class AccountUpdateCubit extends Cubit<AccountUpdateState> {
  AccountUpdateCubit() : super(AccountUpdateState());

  void updateNickname(String nickname) {
    emit(state.copyWith(nickname: nickname));
  }

  void selectCurrency(CurrencyConfig currency) {
    emit(state.copyWith(selectedCurrency: currency));
  }

  void showCurrencyPicker(BuildContext context) {
    final currencies = GlobalConfig().systemConfig.currencyList;
    final currentCurrency = state.selectedCurrency ?? currencies.first;

    CurrencySwitchDialog(
      context,
      currentCurrency: currentCurrency,
      dataList: currencies,
      onClickCurrency: (currency) {
        selectCurrency(currency);
      },
    ).show();
  }

  void submit(BuildContext context) async {
    // Currency validation
    if (state.selectedCurrency == null) {
      GSEasyLoading.showToast('please_select_currency'.tr());
      return;
    }

    if (state.isSubmitting) return;

    emit(state.copyWith(isSubmitting: true));
    GSEasyLoading.showLoading();

    bool success = true;

    try {
      // Update currency (mandatory)
      final currencySuccess = await UserApi.updateCurrency(state.selectedCurrency!.id);
      if (!currencySuccess) {
        success = false;
      } else {
        // Update user info after currency change
        sl<UserCubit>().fetchUserInfo();
        sl<UserCubit>().fetchUserBalance();
        sl<UserCubit>().fetchUserVip();
      }

      // Update nickname (optional)
      if (success && state.nickname.trim().isNotEmpty) {
        final nicknameSuccess = await UserApi.updateNickName(state.nickname.trim());
        if (!nicknameSuccess) {
          success = false;
        } else {
          // Update user info after nickname change
          sl<UserCubit>().fetchUserInfo();
        }
      }

      if (success) {
        GSEasyLoading.showToast('update_successful'.tr());
        sl<NavigatorService>().pop();
      } else {
        GSEasyLoading.showToast('update_failed'.tr());
      }
    } catch (e) {
      GSEasyLoading.showToast('update_failed'.tr());
    } finally {
      GSEasyLoading.dismiss();
      emit(state.copyWith(isSubmitting: false));
    }
  }

  @override
  Future<void> close() {
    state.nicknameController.dispose();
    return super.close();
  }
}

// State class for the dialog
class AccountUpdateState {
  final String nickname;
  final CurrencyConfig? selectedCurrency;
  final TextEditingController nicknameController;
  final bool isSubmitting;

  AccountUpdateState({
    this.nickname = '',
    this.selectedCurrency,
    this.isSubmitting = false,
    TextEditingController? nicknameController,
  }) : nicknameController = nicknameController ?? TextEditingController();

  bool get canSubmit => selectedCurrency != null;

  AccountUpdateState copyWith({
    String? nickname,
    CurrencyConfig? selectedCurrency,
    bool? isSubmitting,
  }) {
    return AccountUpdateState(
      nickname: nickname ?? this.nickname,
      selectedCurrency: selectedCurrency ?? this.selectedCurrency,
      isSubmitting: isSubmitting ?? this.isSubmitting,
      nicknameController: nicknameController,
    );
  }
}
