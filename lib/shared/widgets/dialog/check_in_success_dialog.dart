import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/constants/base64_image.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/hot_push_image.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

class CheckInSuccessDialog {
  final num money;
  final int days;

  CheckInSuccessDialog({
    required this.money,
    required this.days,
  });

  show(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return _CheckInSuccessDialogContent(
          money: money,
          days: days,
        );
      },
    );
  }
}

class _CheckInSuccessDialogContent extends StatelessWidget {
  final num money;
  final int days;

  const _CheckInSuccessDialogContent({
    required this.money,
    required this.days,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: Container(
          width: 362.gw,
          padding: EdgeInsets.all(24.gw),
          decoration: BoxDecoration(
            image: const DecorationImage(image: AssetImage("assets/images/alert/bg_common.png"), fit: BoxFit.fill),
            borderRadius: BorderRadius.circular(16.gw),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildTitle(context),
              SizedBox(height: 24.gw),
              _buildGoldImageWidget(),
              SizedBox(height: 24.gw),

              AneText(
                '$money ${'act_coin'.tr()}',
                style: TextStyle(
                  fontSize: 32.gw,
                  fontWeight: FontWeight.w600,
                  color: context.theme.primaryColor,
                ),
              ),
              AneText(
                'you_obtained'.tr(),
                style: context.textTheme.title.fs16,
              ),
              SizedBox(height: 24.gw),
              CommonButton(
                title: 'accept_it_now'.tr(),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTitle(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        AneText(
          'check_in'.tr(),
          style: context.textTheme.secondary.fs24.w600,
        ),
        SizedBox(width: 5.gw),
        AneText(
          "successfully".tr(),
          style: context.textTheme.primary.fs24.w600.copyWith(color: context.colorTheme.btnBgPrimary),
        ),
      ],
    );
  }

  _buildGoldImageWidget() {
    return HotPushImage(
      imagePath: "assets/images/check_in/icon_gold_success.png",
      base64String: Base64Image.checkInSuccessCoinIconData,
      height: 162.gw,
    );
  }
}
