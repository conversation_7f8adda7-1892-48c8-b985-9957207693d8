import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:wd/core/models/country.dart';
import 'package:wd/core/services/country_service.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/country_picker/filterable_country_picker.dart';

class PhoneInputField extends StatefulWidget {
  final TextEditingController controller;
  final String? hintText;
  final String? errorText;
  final ValueChanged<String>? onChanged;
  final ValueChanged<Country>? onCountryChanged;
  final Country? selectedCountry;
  final bool enabled;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final VoidCallback? onTap;

  const PhoneInputField({
    super.key,
    required this.controller,
    this.hintText,
    this.errorText,
    this.onChanged,
    this.onCountryChanged,
    this.selectedCountry,
    this.enabled = true,
    this.prefixIcon,
    this.suffixIcon,
    this.onTap,
  });

  @override
  State<PhoneInputField> createState() => _PhoneInputFieldState();
}

class _PhoneInputFieldState extends State<PhoneInputField> {
  Country? _selectedCountry;
  final GlobalKey _prefixKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _initializeCountry();
  }

  Future<void> _initializeCountry() async {
    if (widget.selectedCountry != null) {
      _selectedCountry = widget.selectedCountry;
    } else {
      _selectedCountry = await CountryService.instance.getDefaultCountry();
    }
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Phone number input field with clickable country prefix
        _buildPhoneInputField(),

        // Error text
        if (widget.errorText != null) ...[
          SizedBox(height: 4.gw),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 12.gw),
            child: Text(
              widget.errorText!,
              style: TextStyle(
                fontSize: 12.gw,
                color: Colors.red,
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// Shows filterable country picker
  void _showCountryPicker() async {
    final selectedCountry = await FilterableCountryPicker.show(
      context: context,
      anchorKey: _prefixKey,
      selectedCountry: _selectedCountry,
      title: 'select_country'.tr(),
    );

    if (selectedCountry != null) {
      setState(() {
        _selectedCountry = selectedCountry;
      });
      widget.onCountryChanged?.call(selectedCountry);
    }
  }

  /// Builds phone input field with IconTextfield-style design
  Widget _buildPhoneInputField() {
    return SizedBox(
      width: double.infinity,
      height: 60.gw,
      child: TextField(
        controller: widget.controller,
        enabled: widget.enabled,
        onChanged: widget.onChanged,
        onTap: widget.onTap,
        keyboardType: TextInputType.phone,
        inputFormatters: [
          FilteringTextInputFormatter.digitsOnly,
          LengthLimitingTextInputFormatter(15),
        ],
        decoration: InputDecoration(
          hoverColor: Colors.transparent,
          hintText: widget.hintText ?? 'phone_number'.tr(),
          hintStyle: context.textTheme.highlight,
          contentPadding: EdgeInsets.only(
            right: 16.gw,
            top: 16.gh,
            bottom: 16.gh,
          ),
          prefixIconConstraints: BoxConstraints(
            minWidth: 76.gw,
            maxWidth: 76.gw,
          ),
          suffixIcon: widget.suffixIcon,
          prefixIcon: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              GestureDetector(
                onTap: widget.enabled ? _showCountryPicker : null,
                child: Container(
                  key: _prefixKey,
                  width: 56.gw,
                  height: 60.gw,
                  decoration: BoxDecoration(
                    color: context.colorTheme.iconBgA,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(12.gw),
                      bottomLeft: Radius.circular(12.gw),
                    ),
                  ),
                  child: Center(
                      child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _selectedCountry != null
                          ? Text(
                              '+${_selectedCountry!.areaCode}',
                              style: context.textTheme.title,
                            )
                          : Icon(
                              Icons.phone,
                              size: 16.gw,
                              color: context.colorTheme.textSecondary,
                            ),
                      Icon(
                        Icons.arrow_drop_down,
                        size: 18.gw,
                        color: context.colorTheme.textSecondary,
                      ),
                    ],
                  )),
                ),
              ),
              SizedBox(width: 20.gw),
            ],
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(12.gw)),
            borderSide: BorderSide(
              color: context.colorTheme.borderA,
              width: 1,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(12.gw)),
            borderSide: BorderSide(
              color: context.colorTheme.borderA,
              width: 1,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(12.gw)),
            borderSide: BorderSide(
              color: context.theme.primaryColor,
              width: 1,
            ),
          ),
        ),
      ),
    );
  }
}
