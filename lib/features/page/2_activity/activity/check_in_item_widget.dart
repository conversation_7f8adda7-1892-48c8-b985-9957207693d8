import 'package:flutter/material.dart';
import 'package:wd/core/models/entities/daily_check_in_entity.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';
import 'package:wd/core/theme/themes.dart';

class CheckInItemWidget extends StatelessWidget {
  final DailyCheckInItem model;
  final void Function(DailyCheckInItem model) onClickCheckIn;

  const CheckInItemWidget({
    super.key,
    required this.model,
    required this.onClickCheckIn,
  });

  @override
  Widget build(BuildContext context) {
    Widget body = InkWell(
      onTap: () async {
        // 可以补签/未签到
        if (model.signInState == SignInState.needBackdate.value || model.signInState == SignInState.unsigned.value) {
          onClickCheckIn(model);
        }
      },
      child: Container(
        width: 56.gw,
        height: 75.gw,
        decoration: ShapeDecoration(
          color: const Color(0xFF161616),
          shape: RoundedRectangleBorder(
            side: const BorderSide(
              width: 0.50,
              color: Color(0xFF2E2E2E),
            ),
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Column(children: [
          SizedBox(height: 3.gw),
          Stack(
            alignment: Alignment.bottomRight,
            clipBehavior: Clip.none,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(6),
                child: Image.asset(
                  'assets/images/activity/coin_3.png',
                  width: 48.gw,
                  height: 46.gw,
                  fit: BoxFit.contain,
                  alignment: Alignment.center,
                ),
              ),
              Container(
                height: 14,
                padding: EdgeInsets.symmetric(horizontal: 3.gw, vertical: 0.gw),
                decoration: const ShapeDecoration(
                  color: Color(0xB2030303),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(5),
                      bottomRight: Radius.circular(6),
                    ),
                  ),
                ),
                child: AneText(
                  model.signInState == SignInState.needBackdate.value
                      ? "+${model.reSignInAward.formattedMoney}"
                      : "+${model.signInAward.formattedMoney}",
                  style: context.textTheme.secondary.fs12.w500,
                ),
              ),
              if (model.signInState < SignInState.needBackdate.value)
                Positioned(
                  right: 0,
                  top: 0,
                  child: Image.asset(
                    'assets/images/activity/already_used.png',
                    width: 20.gw,
                    height: 20.gw,
                    fit: BoxFit.contain,
                    alignment: Alignment.center,
                  ),
                ),
            ],
          ),
          AneText(
            model.dayName,
            style: context.textTheme.title,
          ),
        ]),
      ),
    );

    return Stack(
      alignment: Alignment.center,
      clipBehavior: Clip.none,
      children: [
        body,
        // if (model.isFetching) ...[
        // _buildFetchIndicator(),
        // ] else ...[
        //   if (model.signInState == SignInState.needBackdate.value)
        // _buildBackdateTag(),
        // ],
        if (model.signInState == SignInState.unsigned.value) Positioned(top: -17.5.gw, child: _buildTipsImage(context)),
      ],
    );
  }

  // _buildRewardWidget() {
  //   late Widget body;
  //   if (model.signInState < SignInState.needBackdate.value) {
  //     body = _buildCheckImage();
  //   } else {
  //     body = Image.asset(
  //       "assets/images/check_in/icon_gold_${model.signInState == SignInState.unsigned.value ? '7' : '4'}.png",
  //       width: width,
  //       height: 29.56.gw,
  //     );
  //   }
  //   return Container(
  //     width: width,
  //     height: 29.56.gw,
  //     alignment: Alignment.center,
  //     child: body,
  //   );
  // }

  _buildTipsImage(BuildContext context) {
    return Stack(
      alignment: AlignmentDirectional.center,
      children: [
        Image.asset("assets/images/activity/Union.png", height: 24, width: 46),
        Positioned(
          top: 0,
          child: AneText(
            'act_Allow'.tr(),
            style: TextStyle(
              color: context.colorTheme.btnBgTertiary,
              fontSize: 14.gw,
            ),
          ),
        ),
      ],
    );
  }

// _buildCheckImage() {
//   return HotPushImage(
//     imagePath: "assets/images/check_in/icon_check.png",
//     base64String: Base64Image.checkInCheckImageData,
//     width: 20.gw,
//     height: 20.gw,
//   );
// }

// _buildBackdateTag() {
//   return IgnorePointer(
//     child: Container(
//         padding: EdgeInsets.symmetric(horizontal: 4.gw, vertical: 2.gw),
//         decoration: BoxDecoration(
//           color: const Color(0x80020202),
//           borderRadius: BorderRadius.all(Radius.circular(2.gw)),
//         ),
//         child: Text(
//           'act_make_up'.tr(),
//           style: TextStyle(color: Colors.white, fontSize: 10.gw),
//           strutStyle: StrutStyle(fontSize: 10.gw),
//         )),
//   );
// }

// _buildFetchIndicator() {
//   return SizedBox(
//     width: 15.gw,
//     height: 15.gw,
//     child: CircularProgressIndicator(
//       strokeWidth: 2.gw,
//       color: Colors.green,
//     ),
//   );
// }
}
